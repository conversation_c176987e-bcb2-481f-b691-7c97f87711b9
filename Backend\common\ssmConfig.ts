import { SSMClient, GetParametersByPathCommand } from '@aws-sdk/client-ssm';
import { getErrorHandler, createErrorContext, ConfigurationError, SSMError } from './errorHandler';

/**
 * Configuration interface for all application parameters
 */
export interface AppConfig {
  HUBSPOT_API_KEY: string;
  HUBSPOT_GET_IN_TOUCH_FORM_GUID: string;
  HUBSPOT_AI_READINESS_FORM_GUID: string;
  HUBSPOT_PORTAL_ID: string;
  MAIL_FROM: string;
  MAIL_TO: string;
  SENDGRID_API_KEY: string;
  SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: string;
  SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: string;
  SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: string;
  SLACK_SUCCESS_WEBHOOK_URL: string;
  SLACK_FAILURE_WEBHOOK_URL: string;
  SECOND_RECIPIENT?: string; // Optional parameter
}

/**
 * SSM Parameter Store configuration service
 * Provides centralized configuration management with caching and fallback support
 */
class SSMConfigService {
  private ssmClient: SSMClient;
  private config: AppConfig | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache
  private readonly PARAMETER_PATH = '/maruti_site/env';
  private isInitialized = false;

  constructor() {
    // Initialize SSM client with default configuration
    // AWS SDK will automatically use environment variables, IAM roles, or AWS config
    this.ssmClient = new SSMClient({
      region: process.env.AWS_REGION || 'us-east-1'
    });
  }

  /**
   * Initialize the configuration service
   * This should be called once during application startup
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    const errorHandler = getErrorHandler();
    const context = createErrorContext('initialize', 'SSMConfigService');

    try {
      await this.fetchConfig();
      this.isInitialized = true;
      console.log('✅ SSM Configuration Service initialized successfully');
    } catch (error) {
      // Handle SSM error with fallback to environment variables
      try {
        console.warn('⚠️  SSM initialization failed, attempting environment variable fallback...');
        errorHandler.handleSSMError(error as Error, 'initialize');
        this.loadFromEnvironment();
        this.isInitialized = true;
        console.log('🔄 SSM Configuration Service initialized with environment variable fallback');
      } catch (fallbackError) {
        const configError = new ConfigurationError(
          'Failed to initialize configuration from both SSM and environment variables',
          context
        );
        errorHandler.handleError(configError, context);
        throw configError;
      }
    }
  }

  /**
   * Get the current configuration
   * Automatically refreshes if cache has expired
   */
  async getConfig(): Promise<AppConfig> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const now = Date.now();
    const cacheExpired = now - this.lastFetchTime > this.CACHE_TTL;

    if (!this.config || cacheExpired) {
      try {
        await this.fetchConfig();
      } catch (error) {
        console.error('Failed to refresh config from SSM, using cached/fallback values:', error);
        // If we have cached config, use it; otherwise load from environment
        if (!this.config) {
          this.loadFromEnvironment();
        }
      }
    }

    if (!this.config) {
      throw new Error('Configuration could not be loaded from SSM or environment variables');
    }

    return this.config;
  }

  /**
   * Fetch configuration from SSM Parameter Store
   */
  private async fetchConfig(): Promise<void> {
    try {
      const command = new GetParametersByPathCommand({
        Path: this.PARAMETER_PATH,
        Recursive: true,
        WithDecryption: true,
        MaxResults: 50
      });

      const response = await this.ssmClient.send(command);

      if (!response.Parameters || response.Parameters.length === 0) {
        throw new Error(`No parameters found at path: ${this.PARAMETER_PATH}`);
      }

      // Convert SSM parameters to configuration object
      const ssmConfig: Partial<AppConfig> = {};

      for (const param of response.Parameters) {
        if (param.Name && param.Value) {
          // Remove the path prefix and NEXT_PUBLIC_ prefix to get clean parameter names
          const cleanName = param.Name
            .replace(`${this.PARAMETER_PATH}/NEXT_PUBLIC_`, '')
            .replace(`${this.PARAMETER_PATH}/`, '');

          // Map SSM parameter names to config keys
          switch (cleanName) {
            case 'HUBSPOT_API_KEY':
              ssmConfig.HUBSPOT_API_KEY = param.Value;
              break;
            case 'HUBSPOT_GET_IN_TOUCH_FORM_GUID':
              ssmConfig.HUBSPOT_GET_IN_TOUCH_FORM_GUID = param.Value;
              break;
            case 'HUBSPOT_AI_READINESS_FORM_GUID':
              ssmConfig.HUBSPOT_AI_READINESS_FORM_GUID = param.Value;
              break;
            case 'HUBSPOT_PORTAL_ID':
              ssmConfig.HUBSPOT_PORTAL_ID = param.Value;
              break;
            case 'MAIL_FROM':
              ssmConfig.MAIL_FROM = param.Value;
              break;
            case 'MAIL_TO':
              ssmConfig.MAIL_TO = param.Value;
              break;
            case 'SENDGRID_API_KEY':
              ssmConfig.SENDGRID_API_KEY = param.Value;
              break;
            case 'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID':
              ssmConfig.SENDGRID_CONTACT_US_FORM_TEMPLATE_ID = param.Value;
              break;
            case 'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID':
              ssmConfig.SENDGRID_AI_READINESS_FORM_TEMPLATE_ID = param.Value;
              break;
            case 'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID':
              ssmConfig.SENDGRID_FAILURE_EMAIL_TEMPLATE_ID = param.Value;
              break;
            case 'SLACK_SUCCESS_WEBHOOK_URL':
              ssmConfig.SLACK_SUCCESS_WEBHOOK_URL = param.Value;
              break;
            case 'SLACK_FAILURE_WEBHOOK_URL':
              ssmConfig.SLACK_FAILURE_WEBHOOK_URL = param.Value;
              break;
            case 'SECOND_RECIPIENT':
              ssmConfig.SECOND_RECIPIENT = param.Value;
              break;
            default:
              console.warn(`Unknown SSM parameter: ${param.Name}`);
          }
        }
      }

      // Validate required parameters and merge with fallbacks
      this.config = this.validateAndMergeConfig(ssmConfig);
      this.lastFetchTime = Date.now();

      console.log(`✅ Successfully loaded ${Object.keys(ssmConfig).length} parameters from SSM`);
    } catch (error) {
      const errorHandler = getErrorHandler();
      const context = createErrorContext('fetchConfig', 'SSMConfigService', {
        parameterPath: this.PARAMETER_PATH
      });

      const ssmError = new SSMError(
        `Failed to fetch parameters from SSM path: ${this.PARAMETER_PATH}`,
        error as Error,
        context
      );

      errorHandler.handleError(ssmError, context);
      throw ssmError;
    }
  }

  /**
   * Load configuration from environment variables as fallback
   */
  private loadFromEnvironment(): void {
    console.log('Loading configuration from environment variables');

    this.config = this.validateAndMergeConfig({
      HUBSPOT_API_KEY: process.env.NEXT_PUBLIC_HUBSPOT_API_KEY,
      HUBSPOT_GET_IN_TOUCH_FORM_GUID: process.env.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID,
      HUBSPOT_AI_READINESS_FORM_GUID: process.env.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID,
      HUBSPOT_PORTAL_ID: process.env.NEXT_PUBLIC_HUBSPOT_PORTAL_ID,
      MAIL_FROM: process.env.NEXT_PUBLIC_MAIL_FROM,
      MAIL_TO: process.env.NEXT_PUBLIC_MAIL_TO,
      SENDGRID_API_KEY: process.env.NEXT_PUBLIC_SENDGRID_API_KEY,
      SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID,
      SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID,
      SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
      SLACK_SUCCESS_WEBHOOK_URL: process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
      SLACK_FAILURE_WEBHOOK_URL: process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
      SECOND_RECIPIENT: process.env.NEXT_PUBLIC_SECOND_RECIPENT
    });

    this.lastFetchTime = Date.now();
  }

  /**
   * Validate and merge configuration with fallbacks
   */
  private validateAndMergeConfig(ssmConfig: Partial<AppConfig>): AppConfig {
    const requiredParams = [
      'HUBSPOT_API_KEY',
      'HUBSPOT_GET_IN_TOUCH_FORM_GUID',
      'HUBSPOT_AI_READINESS_FORM_GUID',
      'HUBSPOT_PORTAL_ID',
      'MAIL_FROM',
      'MAIL_TO',
      'SENDGRID_API_KEY',
      'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
      'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
      'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
      'SLACK_SUCCESS_WEBHOOK_URL',
      'SLACK_FAILURE_WEBHOOK_URL'
    ];

    const config: Partial<AppConfig> = { ...ssmConfig };
    const missingParams: string[] = [];

    // Check required parameters and provide fallbacks
    for (const param of requiredParams) {
      if (!config[param as keyof AppConfig]) {
        // Try to get from environment variable as fallback
        const envKey = `NEXT_PUBLIC_${param}`;
        const envValue = process.env[envKey];

        if (envValue) {
          config[param as keyof AppConfig] = envValue;
          console.log(`Using environment variable fallback for ${param}`);
        } else {
          missingParams.push(param);
        }
      }
    }

    if (missingParams.length > 0) {
      throw new Error(`Missing required configuration parameters: ${missingParams.join(', ')}`);
    }

    return config as AppConfig;
  }

  /**
   * Force refresh configuration from SSM
   */
  async refreshConfig(): Promise<AppConfig> {
    this.lastFetchTime = 0; // Force cache expiry
    return await this.getConfig();
  }

  /**
   * Get a specific configuration value
   */
  async getConfigValue<K extends keyof AppConfig>(key: K): Promise<AppConfig[K]> {
    const config = await this.getConfig();
    return config[key];
  }

  /**
   * Check if the service is properly initialized
   */
  isServiceInitialized(): boolean {
    return this.isInitialized && this.config !== null;
  }
}

// Create and export a singleton instance
const ssmConfigService = new SSMConfigService();

export default ssmConfigService;

/**
 * Convenience function to get configuration
 * @returns Promise<AppConfig> The application configuration
 */
export const getConfig = async (): Promise<AppConfig> => {
  return await ssmConfigService.getConfig();
};

/**
 * Convenience function to get a specific configuration value
 * @param key The configuration key to retrieve
 * @returns Promise<T> The configuration value
 */
export const getConfigValue = async <K extends keyof AppConfig>(key: K): Promise<AppConfig[K]> => {
  return await ssmConfigService.getConfigValue(key);
};
