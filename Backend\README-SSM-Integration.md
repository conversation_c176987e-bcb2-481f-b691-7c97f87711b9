# AWS SSM Parameter Store Integration

This document describes the AWS SSM Parameter Store integration implemented for the Backend services. The integration provides centralized configuration management with fallback to environment variables.

## Overview

The Backend services have been updated to fetch configuration from AWS SSM Parameter Store instead of relying solely on environment variables. This provides:

- **Centralized Configuration**: All configuration stored in AWS SSM Parameter Store
- **Security**: Sensitive values encrypted using AWS KMS
- **Fallback Support**: Graceful fallback to environment variables if SSM is unavailable
- **Caching**: Parameter values cached for 5 minutes to reduce AWS API calls
- **Validation**: Comprehensive validation of all required parameters
- **Error Handling**: Robust error handling with detailed logging

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Handler   │───▶│  SSM Config      │───▶│  AWS SSM        │
│   (Lambda)      │    │  Service         │    │  Parameter      │
└─────────────────┘    └──────────────────┘    │  Store          │
                              │                └─────────────────┘
                              ▼
                       ┌──────────────────┐
                       │  Environment     │
                       │  Variables       │
                       │  (Fallback)      │
                       └──────────────────┘
```

## Required SSM Parameters

The following parameters must be configured in AWS SSM Parameter Store:

### HubSpot Configuration
- `/maruti_site/env/NEXT_PUBLIC_HUBSPOT_API_KEY`
- `/maruti_site/env/NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID`
- `/maruti_site/env/NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID`
- `/maruti_site/env/NEXT_PUBLIC_HUBSPOT_PORTAL_ID`

### Email Configuration
- `/maruti_site/env/NEXT_PUBLIC_MAIL_FROM`
- `/maruti_site/env/NEXT_PUBLIC_MAIL_TO`

### SendGrid Configuration
- `/maruti_site/env/NEXT_PUBLIC_SENDGRID_API_KEY`
- `/maruti_site/env/NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID`
- `/maruti_site/env/NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID`
- `/maruti_site/env/NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID`

### Slack Configuration
- `/maruti_site/env/NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL`
- `/maruti_site/env/NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL`

### Optional Parameters
- `/maruti_site/env/NEXT_PUBLIC_SECOND_RECIPIENT`

## Usage

### Basic Configuration Access

```typescript
import { getConfig, getConfigValue } from '../common/ssmConfig';

// Get all configuration
const config = await getConfig();
console.log(config.HUBSPOT_API_KEY);

// Get specific configuration value
const apiKey = await getConfigValue('HUBSPOT_API_KEY');
```

### In API Handlers

```javascript
import { getConfigValue } from "../../common/ssmConfig";

export const handler = async (event) => {
  try {
    // Get configuration values from SSM
    const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
      getConfigValue('HUBSPOT_API_KEY'),
      getConfigValue('HUBSPOT_GET_IN_TOUCH_FORM_GUID')
    ]);

    // Use configuration values
    const payload = {
      headers: {
        Authorization: `Bearer ${hubspotApiKey}`,
      },
    };

    const response = await sendDataToHubspot(
      formData.secondary_source,
      payload,
      hubspotFormGuid
    );
  } catch (error) {
    // Error handling is built-in
    console.error('Configuration error:', error);
  }
};
```

## Configuration Validation

### Automatic Validation

The system includes automatic validation that runs during application startup:

```bash
# Validate configuration manually
npm run validate-config

# Validation runs automatically before start
npm start
```

### Validation Features

- **Required Parameter Check**: Ensures all required parameters are present
- **Format Validation**: Validates email addresses, URLs, and other formats
- **Fallback Testing**: Tests fallback to environment variables
- **Detailed Reporting**: Provides detailed error and warning messages

## Error Handling

### Built-in Error Handling

The SSM configuration service includes comprehensive error handling:

- **SSM Unavailable**: Automatically falls back to environment variables
- **Missing Parameters**: Clear error messages with parameter names
- **Network Issues**: Retry logic and graceful degradation
- **Caching**: Uses cached values when SSM is temporarily unavailable

### Error Types

```typescript
import { ConfigurationError, SSMError } from '../common/errorHandler';

try {
  const config = await getConfig();
} catch (error) {
  if (error instanceof ConfigurationError) {
    // Handle configuration-specific errors
  } else if (error instanceof SSMError) {
    // Handle SSM-specific errors
  }
}
```

## Deployment

### AWS IAM Permissions

The Lambda execution role needs the following permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:GetParametersByPath"
      ],
      "Resource": "arn:aws:ssm:*:*:parameter/maruti_site/env/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "kms:Decrypt"
      ],
      "Resource": "arn:aws:kms:*:*:key/*"
    }
  ]
}
```

### Environment Variables

Set the AWS region for SSM client:

```bash
AWS_REGION=us-east-1
```

### Parameter Store Setup

Create parameters in AWS SSM Parameter Store:

```bash
# Example using AWS CLI
aws ssm put-parameter \
  --name "/maruti_site/env/NEXT_PUBLIC_HUBSPOT_API_KEY" \
  --value "your-hubspot-api-key" \
  --type "SecureString" \
  --description "HubSpot API Key for form submissions"
```

## Monitoring and Logging

### Built-in Logging

The system provides comprehensive logging:

- **Initialization**: SSM service initialization status
- **Parameter Loading**: Number of parameters loaded from SSM
- **Fallback Usage**: When environment variables are used as fallback
- **Errors**: Detailed error messages with context
- **Validation**: Configuration validation results

### Log Examples

```
✅ SSM Configuration Service initialized successfully
✅ Successfully loaded 12 parameters from SSM
🔄 Using fallback value for SSM operation: getParameter
⚠️ SSM initialization failed, attempting environment variable fallback...
❌ Configuration validation failed
```

## Performance

### Caching Strategy

- **Cache Duration**: 5 minutes (configurable)
- **Cache Invalidation**: Automatic refresh when cache expires
- **Manual Refresh**: `refreshConfig()` method available
- **Memory Efficient**: Single configuration object cached

### API Call Optimization

- **Batch Loading**: Uses `getParametersByPath` for efficient loading
- **Parallel Requests**: Multiple parameter requests handled in parallel
- **Minimal Calls**: Configuration cached to minimize AWS API calls

## Troubleshooting

### Common Issues

1. **Missing IAM Permissions**
   - Ensure Lambda has SSM and KMS permissions
   - Check parameter path permissions

2. **Parameter Not Found**
   - Verify parameter exists in SSM Parameter Store
   - Check parameter path spelling
   - Ensure correct AWS region

3. **Validation Failures**
   - Run `npm run validate-config` for detailed errors
   - Check parameter formats (emails, URLs)
   - Verify all required parameters are set

### Debug Mode

Enable detailed logging by setting:

```bash
NODE_ENV=development
```

## Migration Guide

### From Environment Variables

1. **Create SSM Parameters**: Add all environment variables to SSM Parameter Store
2. **Update IAM Roles**: Add SSM permissions to Lambda execution roles
3. **Test Configuration**: Run validation script to ensure all parameters are accessible
4. **Deploy**: Deploy updated Lambda functions
5. **Monitor**: Check logs for successful SSM initialization

### Rollback Plan

If issues occur, the system automatically falls back to environment variables, ensuring zero downtime during migration.
