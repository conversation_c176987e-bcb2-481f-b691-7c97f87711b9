import ssmConfigService, { AppConfig } from './ssmConfig';

/**
 * Configuration validation result
 */
interface ValidationResult {
  isValid: boolean;
  missingParameters: string[];
  errors: string[];
  warnings: string[];
}

/**
 * Validate that all required configuration parameters are available
 * @returns Promise<ValidationResult> Validation result with details
 */
export async function validateConfiguration(): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    missingParameters: [],
    errors: [],
    warnings: []
  };

  try {
    console.log('🔍 Starting configuration validation...');
    
    // Initialize the SSM configuration service
    await ssmConfigService.initialize();
    
    if (!ssmConfigService.isServiceInitialized()) {
      result.errors.push('SSM Configuration Service failed to initialize');
      result.isValid = false;
      return result;
    }

    // Get the configuration
    const config = await ssmConfigService.getConfig();
    
    // Define required parameters
    const requiredParams: (keyof AppConfig)[] = [
      'HUBSPOT_API_KEY',
      'HUBSPOT_GET_IN_TOUCH_FORM_GUID',
      'HUBSPOT_AI_READINESS_FORM_GUID',
      'HUBSPOT_PORTAL_ID',
      'MAIL_FROM',
      'MAIL_TO',
      'SENDGRID_API_KEY',
      'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
      'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
      'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
      'SLACK_SUCCESS_WEBHOOK_URL',
      'SLACK_FAILURE_WEBHOOK_URL'
    ];

    // Optional parameters
    const optionalParams: (keyof AppConfig)[] = [
      'SECOND_RECIPIENT'
    ];

    // Validate required parameters
    for (const param of requiredParams) {
      const value = config[param];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        result.missingParameters.push(param);
        result.errors.push(`Required parameter '${param}' is missing or empty`);
        result.isValid = false;
      }
    }

    // Check optional parameters and warn if missing
    for (const param of optionalParams) {
      const value = config[param];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        result.warnings.push(`Optional parameter '${param}' is not configured`);
      }
    }

    // Validate specific parameter formats
    await validateParameterFormats(config, result);

    // Log validation results
    if (result.isValid) {
      console.log('✅ Configuration validation passed');
      console.log(`📊 Validated ${requiredParams.length} required parameters`);
      if (result.warnings.length > 0) {
        console.log(`⚠️  ${result.warnings.length} warnings found:`);
        result.warnings.forEach(warning => console.log(`   - ${warning}`));
      }
    } else {
      console.error('❌ Configuration validation failed');
      console.error(`🚫 ${result.errors.length} errors found:`);
      result.errors.forEach(error => console.error(`   - ${error}`));
    }

  } catch (error: any) {
    result.errors.push(`Configuration validation error: ${error.message}`);
    result.isValid = false;
    console.error('💥 Configuration validation threw an error:', error);
  }

  return result;
}

/**
 * Validate parameter formats and values
 */
async function validateParameterFormats(config: AppConfig, result: ValidationResult): Promise<void> {
  // Validate email addresses
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (config.MAIL_FROM && !emailRegex.test(config.MAIL_FROM)) {
    result.errors.push(`MAIL_FROM '${config.MAIL_FROM}' is not a valid email address`);
    result.isValid = false;
  }
  
  if (config.MAIL_TO && !emailRegex.test(config.MAIL_TO)) {
    result.errors.push(`MAIL_TO '${config.MAIL_TO}' is not a valid email address`);
    result.isValid = false;
  }

  if (config.SECOND_RECIPIENT && !emailRegex.test(config.SECOND_RECIPIENT)) {
    result.errors.push(`SECOND_RECIPIENT '${config.SECOND_RECIPIENT}' is not a valid email address`);
    result.isValid = false;
  }

  // Validate webhook URLs
  const urlRegex = /^https?:\/\/.+/;
  
  if (config.SLACK_SUCCESS_WEBHOOK_URL && !urlRegex.test(config.SLACK_SUCCESS_WEBHOOK_URL)) {
    result.errors.push(`SLACK_SUCCESS_WEBHOOK_URL is not a valid URL`);
    result.isValid = false;
  }
  
  if (config.SLACK_FAILURE_WEBHOOK_URL && !urlRegex.test(config.SLACK_FAILURE_WEBHOOK_URL)) {
    result.errors.push(`SLACK_FAILURE_WEBHOOK_URL is not a valid URL`);
    result.isValid = false;
  }

  // Validate HubSpot Portal ID (should be numeric)
  if (config.HUBSPOT_PORTAL_ID && !/^\d+$/.test(config.HUBSPOT_PORTAL_ID)) {
    result.errors.push(`HUBSPOT_PORTAL_ID should be numeric`);
    result.isValid = false;
  }

  // Validate SendGrid template IDs (should be valid template ID format)
  const templateIdRegex = /^d-[a-f0-9]{32}$/;
  const templateIds = [
    { key: 'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID', value: config.SENDGRID_CONTACT_US_FORM_TEMPLATE_ID },
    { key: 'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID', value: config.SENDGRID_AI_READINESS_FORM_TEMPLATE_ID },
    { key: 'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID', value: config.SENDGRID_FAILURE_EMAIL_TEMPLATE_ID }
  ];

  for (const template of templateIds) {
    if (template.value && !templateIdRegex.test(template.value)) {
      result.warnings.push(`${template.key} '${template.value}' may not be a valid SendGrid template ID format`);
    }
  }
}

/**
 * Validate configuration and exit process if validation fails
 * This function should be called during application startup
 */
export async function validateConfigurationOrExit(): Promise<void> {
  const validation = await validateConfiguration();
  
  if (!validation.isValid) {
    console.error('\n🚨 APPLICATION STARTUP FAILED 🚨');
    console.error('Configuration validation failed. Please check your AWS SSM Parameter Store configuration.');
    console.error('\nRequired SSM Parameters:');
    console.error('- /maruti_site/env/NEXT_PUBLIC_HUBSPOT_API_KEY');
    console.error('- /maruti_site/env/NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID');
    console.error('- /maruti_site/env/NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID');
    console.error('- /maruti_site/env/NEXT_PUBLIC_HUBSPOT_PORTAL_ID');
    console.error('- /maruti_site/env/NEXT_PUBLIC_MAIL_FROM');
    console.error('- /maruti_site/env/NEXT_PUBLIC_MAIL_TO');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SENDGRID_API_KEY');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL');
    console.error('\nOptional SSM Parameters:');
    console.error('- /maruti_site/env/NEXT_PUBLIC_SECOND_RECIPIENT');
    console.error('\nAlternatively, ensure these environment variables are set as fallbacks.');
    
    process.exit(1);
  }
}

/**
 * Get a summary of the current configuration status
 */
export async function getConfigurationSummary(): Promise<string> {
  try {
    const validation = await validateConfiguration();
    
    let summary = '📋 Configuration Summary:\n';
    summary += `Status: ${validation.isValid ? '✅ Valid' : '❌ Invalid'}\n`;
    summary += `Required Parameters: ${validation.missingParameters.length === 0 ? 'All present' : `${validation.missingParameters.length} missing`}\n`;
    summary += `Errors: ${validation.errors.length}\n`;
    summary += `Warnings: ${validation.warnings.length}\n`;
    
    if (validation.errors.length > 0) {
      summary += '\nErrors:\n';
      validation.errors.forEach(error => summary += `  - ${error}\n`);
    }
    
    if (validation.warnings.length > 0) {
      summary += '\nWarnings:\n';
      validation.warnings.forEach(warning => summary += `  - ${warning}\n`);
    }
    
    return summary;
  } catch (error: any) {
    return `❌ Failed to get configuration summary: ${error.message}`;
  }
}
